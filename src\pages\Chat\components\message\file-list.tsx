import { FileJpgOutlined, FileTextOutlined } from '@ant-design/icons';
import React from 'react';
import { PhotoProvider, PhotoView } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';

import { IMessageFileItem } from '@/types';
import { formatSize } from '@/utils';

interface IMessageFileListProps {
  /**
   * 消息附件列表
   */
  files?: IMessageFileItem[];
  animate?: boolean;
}

/**
 * 消息附件列表展示组件
 */
export default function MessageFileList(props: IMessageFileListProps) {
  const { files, animate = true } = props;
  const [renderedFiles, setRenderedFiles] = React.useState<any[]>([]);

  React.useEffect(() => {
    if (!animate) {
      setRenderedFiles(files);
      return;
    }
    if (files.length > renderedFiles.length) {
      setTimeout(() => {
        setRenderedFiles(files.slice(0, renderedFiles.length + 1));
      }, 100);
    }
  }, [files, animate, renderedFiles.length]);

  if (!files?.length) {
    return null;
  }

  const visibleFiles = renderedFiles;

  const isAllImages = files.every((item) => item.type === 'image');

  // 如果所有文件都是图片，则直接展示图片列表
  if (isAllImages) {
    return (
      <div className="flex flex-wrap">
        {visibleFiles.map((item: IMessageFileItem) => {
          return (
            <PhotoProvider key={item.id}>
              <PhotoView src={item.url}>
                <img
                  src={item.url}
                  key={item.id}
                  alt={item.filename}
                  className="w-24 h-24 cursor-zoom-in mr-2 rounded-lg"
                  style={{
                    objectFit: 'cover',
                  }}
                />
              </PhotoView>
            </PhotoProvider>
          );
        })}
      </div>
    );
  }

  // 如果存在非图片文件，则展示文件列表
  return (
    <div>
      {visibleFiles.map((file, idx) => (
        <div
          key={file.id || idx}
          className={animate && idx === visibleFiles.length - 1 ? 'fade-in' : ''}
        >
          <a
            title="点击下载文件"
            href={file.url}
            target="_blank"
            rel="noreferrer"
            className="p-3 bg-gray-50 rounded-lg w-60 flex items-center cursor-pointer no-underline mb-2"
          >
            {file.type === 'image' ? (
              <FileJpgOutlined className="text-3xl text-gray-400 mr-2" />
            ) : (
              <FileTextOutlined className="text-3xl text-gray-400 mr-2" />
            )}
            <div className="overflow-hidden">
              <div className="text-default truncate">{file.filename}</div>
              {file.size ? <div className="text-desc truncate">{formatSize(file.size)}</div> : null}
            </div>
          </a>
        </div>
      ))}
    </div>
  );
}
