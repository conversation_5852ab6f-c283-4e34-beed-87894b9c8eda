// @ts-ignore
/* eslint-disable */
import http from '@/utils/fetch';
import { request } from '@umijs/max';
import { ChatAPI } from './typeings';

/** 发送信息 POST http://192.168.166.58:8000/stream?prompt=雷猴 */
export function sendMessage(data: ChatAPI.SendMessageReq, options?: { [key: string]: any }) {
  return http.post('/demo/run_agent', data, {
    headers: {
      Accept: 'text/event-stream',
      'Content-Type': 'application/json',
    },
    ...options,
  });
}

/** 取消代理 GET /demo/cancel_agent */
export async function cancelAgent(
  params: ChatAPI.CancelAgentReq,
  options?: { [key: string]: any },
) {
  return request('/demo/cancel_agent', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取当前的用户 GET /api/get_user_info */
export async function getUserSession(
  params: ChatAPI.GetUserInfoReq,
  options?: { [key: string]: any },
) {
  return request<ChatAPI.GetUserInfoRes>('/demo/crud/get_user_session', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 获取会话列表 GET /api/get_session_info */
export async function getSessionInfo(
  params: ChatAPI.GetSessionInfoReq,
  options?: { [key: string]: any },
) {
  return request<ChatAPI.GetSessionInfoRes>('/demo/crud/get_session_info', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 创建会话 GET /api/api_create_session */
export async function createSession(
  params: ChatAPI.GetUserInfoReq,
  options?: { [key: string]: any },
) {
  return request<ChatAPI.CreateSessionReq>('/demo/crud/api_create_session', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 会话重命名 POST /file/crud/upload */
export async function renameSession(
  data: ChatAPI.RenameSessionReq,
  options?: { [key: string]: any },
) {
  return request<ChatAPI.UploadFileReq[]>('/demo/crud/rename_session', {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

/** 删除会话 GET /demo/crud/delete_session */
export async function deleteSession(
  params: ChatAPI.DeleteSessionReq,
  options?: { [key: string]: any },
) {
  return request<ChatAPI.CreateSessionReq>('/demo/crud/delete_session', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}

/** 文件上传 POST /file/crud/upload */
export async function uploadFile(data: FormData, options?: { [key: string]: any }) {
  return request<ChatAPI.UploadFileReq[]>('/demo/crud/upload', {
    method: 'POST',
    data,
    ...(options || {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),
  });
}

/** 获取提示集 GET /demo/get_tips */
export async function getTips(options?: { [key: string]: any }) {
  return request<ChatAPI.GetTipsRes>('/demo/get_tips', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用户评论 POST /demo/crud/user/comment */
export async function comment(data: ChatAPI.CommentReq, options?: { [key: string]: any }) {
  return request<ChatAPI.CommentRes>('/demo/crud/user/comment', {
    method: 'POST',
    data,
    ...(options || {}),
  });
}
