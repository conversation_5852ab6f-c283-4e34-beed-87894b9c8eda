import { Spin } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

import { RoleType } from '@/enums';
import { useLatest } from '@/hooks/use-latest';
import { useX } from '@/hooks/useX';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import { IAgentMessage, IMessageFileItem, IMessageItem4Render } from '@/types';
import { isTempId } from '@/utils';
import { Conversation } from '@ant-design/x/es/conversations';
import { IGetAppParametersResponse } from '../type';
import { Chatbox } from './ChatBox';

interface IChatboxWrapperProps {
  /**
   * 应用参数
   */
  appParameters?: IGetAppParametersResponse;
  /**
   * 当前对话 ID
   */
  conversationId?: string;
  /**
   * 当前对话名称
   */
  conversationName?: string;
  /**
   * 对话列表
   */
  conversationItems: Conversation[];
  /**
   * 对话 ID 变更时触发的回调函数
   * @param id 即将变更的对话 ID
   */
  onConversationIdChange: (id: string) => void;
  /**
   * 对话列表变更时触发的回调函数
   */
  onItemsChange: (items: Conversation[]) => void;
  /**
   * 内部处理对话列表变更的函数
   */
  conversationItemsChangeCallback: () => void;
}

export default function ChatboxWrapper(props: IChatboxWrapperProps) {
  const { appParameters, conversationId, onConversationIdChange, conversationItemsChangeCallback } =
    props;

  const abortRef = useRef<() => void>(() => {});
  const [initLoading, setInitLoading] = useState<boolean>(false);
  const [historyMessages, setHistoryMessages] = useState<IMessageItem4Render[]>([]);
  const [inputParams] = useState<{ [key: string]: unknown }>({});

  // 用于跟踪上一次的conversationId，以区分真正的会话切换和临时ID更新
  const prevConversationIdRef = useRef<string | undefined>(conversationId);

  // 定义 ref, 用于获取最新的 conversationId
  const latestProps = useLatest({
    conversationId,
  });
  const latestState = useLatest({
    inputParams,
  });

  const filesRef = useRef<IMessageFileItem[]>([]);

  /**
   * 获取对话的历史消息
   */
  const abortControllerRef = useRef<AbortController | null>(null);
  const getConversationMessages = async (conversationId?: string, signal?: AbortSignal) => {
    // 如果是临时 ID，则不获取历史消息
    if (isTempId(conversationId)) {
      return;
    }
    const result = await chat.api.getSessionInfo(
      {
        session_id: conversationId,
      },
      { signal },
    );
    if (!result?.messages?.length) {
      return;
    }

    const newMessages: IMessageItem4Render[] = [];

    // 只有当历史消息中的参数不为空时才更新
    // if (result?.messages?.length && Object.values(result.messages?.[0]?.inputs)?.length) {
    //   setInputParams(result.messages[0]?.inputs || {});
    // }

    result.messages.forEach((item: ChatAPI.Message) => {
      if (item.event_type === 'user_message') {
        newMessages.push({
          id: String(item.message_id),
          content: item.content || '',
          status: 'success',
          isHistory: true,
          // files: item.message_files,
          role: RoleType.user,
          created_at: item.created_time,
        });
      } else if (item.event_type === 'system_message') {
        const handlePlan: IAgentMessage['contentBlocks'] =
          item.plans?.map((item: ChatAPI.Plans) => {
            return {
              type: 'workflow',
              content: '',
              workflows: {
                status: 'finished',
                nodes: item.actions?.map((subItem: ChatAPI.Action) => {
                  return {
                    id: String(subItem.action_id),
                    status: 'success',
                    type: 'question-classifier',
                    action_process: subItem.action_process,
                    title: subItem.action_description || '',
                    outputs: subItem.action_result?.action_result || '',
                    elapsed_time: subItem.action_duration,
                  };
                }),
              },
            };
          }) || [];
        const contentBlocks: IAgentMessage['contentBlocks'] = [
          {
            type: 'content',
            content: `${item.intention_comprehend || ''}${item.intention_think || ''}`,
          },
          ...handlePlan,
          {
            type: 'content',
            content: item.intention_result || '',
          },
        ];

        newMessages.push({
          id: String(item.intention_id),
          content: `${item.intention_comprehend || ''}${item.intention_think || ''}\n${
            item.intention_result || ''
          }`,
          status: 'success',
          error: '',
          isHistory: true,
          feedback: {
            rating: item.is_good,
          },
          role: RoleType.ai,
          contentBlocks,
          created_at: item.created_time,
        });
      }
    });
    console.log('newMessages:', newMessages);

    setMessages([]);
    setHistoryMessages(newMessages);
  };

  const { agent, onRequest, messages, setMessages, currentMessageId } = useX({
    latestProps,
    latestState,
    filesRef,
    appParameters,
    abortRef,
    getConversationMessages,
    onConversationIdChange,
    conversationItemsChangeCallback,
  });

  const initConversationInfo = async (signal?: AbortSignal) => {
    try {
      // 有对话 ID 且非临时 ID 时，获取历史消息
      setInitLoading(true);
      if (conversationId && !isTempId(conversationId)) {
        await getConversationMessages(conversationId, signal);
      } else {
        setMessages([]);
        setHistoryMessages([]);
      }
    } catch (err) {
      console.error(err);
    } finally {
      setInitLoading(false);
    }
  };

  // 会话切换时，清空状态
  useEffect(() => {
    const prevConversationId = prevConversationIdRef.current;

    // 判断是否为临时ID更新为真实ID的情况
    const isTempIdUpdate =
      prevConversationId &&
      isTempId(prevConversationId) &&
      conversationId &&
      !isTempId(conversationId);

    console.log('会话ID变化检测:', {
      prev: prevConversationId,
      current: conversationId,
      isTempIdUpdate,
      shouldReload: !isTempIdUpdate,
    });

    // 如果是临时ID更新为真实ID，不需要重新加载内容
    if (isTempIdUpdate) {
      console.log('🔄 临时ID更新为真实ID，保持当前聊天内容');
      prevConversationIdRef.current = conversationId;
      conversationItemsChangeCallback();
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    // 创建新的 AbortController
    abortControllerRef.current = new AbortController();

    filesRef.current = []; // 清空文件引用
    initConversationInfo(abortControllerRef.current.signal);

    // 更新ref
    prevConversationIdRef.current = conversationId;

    // 组件卸载时清理
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [conversationId, conversationItemsChangeCallback]);

  const onSubmit = (nextContent: string, options?: { files?: IMessageFileItem[] }) => {
    filesRef.current = options?.files || [];
    onRequest({
      stream: true,
      content: nextContent,
      files: options?.files as IMessageFileItem[],
    });
  };

  const unStoredMessages4Render = useMemo(() => {
    return (
      messages?.map((item) => {
        return {
          id: item.id,
          requestStatus: item.message.requestStatus,
          status: item.status,
          message_id: item.message?.message_id,
          // @ts-expect-error TODO: 类型待优化
          error: item.message.error || '',
          workflows: item.message.workflows,
          content: item.message.content,
          role: item.status === 'local' ? 'user' : 'ai',
          contentBlocks: item.message.contentBlocks,
        } as IMessageItem4Render;
      }) || []
    );
  }, [messages]);

  return (
    <div className="flex h-screen flex-col overflow-hidden flex-1">
      <div className="flex-1 overflow-hidden relative">
        {initLoading ? (
          <div className="absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center">
            <Spin spinning />
          </div>
        ) : null}

        <Chatbox
          conversationId={conversationId}
          appParameters={appParameters}
          messageItems={[...historyMessages, ...unStoredMessages4Render]}
          isRequesting={agent.isRequesting()}
          onSubmit={onSubmit}
          onCancel={async () => {
            abortRef.current();
            chat.api.cancelAgent({
              message_id: currentMessageId,
            });
          }}
        />
      </div>
    </div>
  );
}
