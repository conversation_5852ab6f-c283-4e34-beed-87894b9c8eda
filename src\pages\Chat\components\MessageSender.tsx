import { CloudUploadOutlined, LinkOutlined } from '@ant-design/icons';
import { Attachments, AttachmentsProps, Sender } from '@ant-design/x';
import { Badge, Button, GetProp, GetRef, message } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { ALLOWED_FILE_TYPES } from '@/config';
import { IMessageFileItem } from '@/types';
import { getFileExtByName } from '@/utils';
import { RcFile } from 'antd/es/upload';

interface IMessageSenderProps {
  /**
   * 类名
   */
  className?: string;
  /**
   * 当前输出的文字
   */
  content: string;
  setContent: (value: string) => void;
  /**
   * 是否正在请求
   */
  isRequesting: boolean;
  /**
   * 上传文件 Api (不再使用，由内部 XMLHttpRequest 实现)
   */
  uploadFileApi: (file: FormData, options?: any) => Promise<any>;
  /**
   * 输入框 change 事件
   */
  onChange: (value: string) => void;
  /**
   * 提交事件
   * @param value 问题-文本
   * @param files 问题-文件
   */
  onSubmit: (
    value: string,
    options?: {
      files?: IMessageFileItem[];
    },
  ) => void;
  /**
   * 取消事件
   */
  onCancel: () => void;
  /**
   * 当前会话ID
   */
  conversationId?: string;
}

/**
 * 用户消息发送区
 */
export const MessageSender = (props: IMessageSenderProps) => {
  const {
    content,
    setContent,
    isRequesting,
    onChange,
    onSubmit,
    className,
    onCancel,
    uploadFileApi,
    conversationId,
  } = props;
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState<GetProp<AttachmentsProps, 'items'>>([]);

  // 用于跟踪上一次的conversationId，以区分真正的会话切换和临时ID更新
  const prevConversationIdRef = useRef<string | undefined>(conversationId);

  const allowedFileTypes = ALLOWED_FILE_TYPES;
  const handleUpload = async (file: RcFile) => {
    // 检查文件是否已经存在于列表中
    const fileExists = files.some((existingFile) => existingFile.uid === file.uid);
    if (fileExists) {
      console.log('文件已存在，跳过上传', file.name);
      return;
    }

    const fileBaseInfo: GetProp<AttachmentsProps, 'items'>[number] = {
      uid: file.uid,
      name: file.name,
      status: 'uploading',
      size: file.size,
      type: file.type,
      originFileObj: file,
      percent: 0,
    };

    setFiles((prevFiles) => [...prevFiles, fileBaseInfo]);

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await uploadFileApi(formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent: any) => {
          if (progressEvent.total) {
            // 计算上传进度百分比
            const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            // 更新文件上传进度
            setFiles((currentFiles) => {
              return currentFiles.map((item) => {
                if (item.uid === file.uid) {
                  return {
                    ...item,
                    percent,
                  };
                }
                return item;
              });
            });
          }
        },
      });

      const result = response;
      // 更新文件状态为完成
      setFiles((currentFiles) => {
        return currentFiles.map((item) => {
          if (item.uid === file.uid) {
            return {
              ...item,
              url: result[0]?.url,
              percent: 100,
              status: 'done',
            };
          }
          return item;
        });
      });

      return result;
    } catch (error) {
      console.error('文件上传失败:', error);
      // 更新文件状态为错误
      setFiles((currentFiles) => {
        return currentFiles.map((item) => {
          if (item.uid === file.uid) {
            return {
              ...item,
              percent: 0,
              status: 'error',
            };
          }
          return item;
        });
      });

      message.error(`文件 ${file.name} 上传失败`);
    }
  };

  // const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
  //   // 检测 Ctrl/Cmd + Enter
  //   console.log('handleKeyDown', e.key);

  //   if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
  //     e.preventDefault();
  //     // insertNewline(e.target);
  //   }

  //   // 普通 Enter 处理
  //   if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
  //     // e.stopImmediatePropagation();
  //     e.preventDefault();
  //     // handleSend();
  //   }
  // };

  // const insertNewline = (textarea: any) => {
  //   const start = textarea.selectionStart;
  //   const end = textarea.selectionEnd;
  //   const newValue = content.slice(0, start) + '\n' + content.slice(end);

  //   setContent(newValue);

  //   // 保持光标位置
  //   setTimeout(() => {
  //     textarea.selectionStart = textarea.selectionEnd = start + 1;
  //   }, 0);
  // };

  const senderRef = useRef<GetRef<typeof Sender>>(null);
  const senderHeader = (
    <Sender.Header
      title="上传文件"
      open={open}
      onOpenChange={setOpen}
      styles={{
        content: {
          padding: 0,
        },
      }}
    >
      <Attachments
        beforeUpload={async (file, fileList) => {
          console.log('beforeUpload', file, fileList);
          const ext = getFileExtByName(file.name);
          // 校验文件类型
          if (allowedFileTypes.length > 0 && !allowedFileTypes.includes(ext!.toLocaleLowerCase())) {
            message.error(`不支持的文件类型: ${ext}`);
            return false;
          }

          // 处理当前文件
          handleUpload(file);
          return false;
        }}
        multiple
        items={files}
        placeholder={(type) =>
          type === 'drop'
            ? {
                title: '点击或拖拽文件到此区域上传',
              }
            : {
                icon: <CloudUploadOutlined />,
                title: '点击或拖拽文件到此区域上传',
                description: (
                  <div>
                    支持的文件类型：
                    {allowedFileTypes.join(', ')}
                  </div>
                ),
              }
        }
        getDropContainer={() => senderRef.current?.nativeElement}
        onRemove={(file) => {
          setFiles((prev) => {
            return prev.filter((item) => {
              return item.uid !== file.uid;
            });
          });
        }}
      />
    </Sender.Header>
  );

  // 当会话ID变化时清空文件（但排除临时ID更新的情况）
  useEffect(() => {
    const prevConversationId = prevConversationIdRef.current;

    // 判断是否为临时ID更新为真实ID的情况
    const isTempIdUpdate =
      prevConversationId &&
      prevConversationId.startsWith('temp_') && // 使用字符串检查代替 isTempId
      conversationId &&
      !conversationId.startsWith('temp_');

    console.log('MessageSender 会话ID变化检测:', {
      prev: prevConversationId,
      current: conversationId,
      isTempIdUpdate,
      shouldClear: !isTempIdUpdate,
    });

    // 如果是临时ID更新为真实ID，不清空输入内容
    if (isTempIdUpdate) {
      console.log('🔄 MessageSender: 临时ID更新，保持输入内容');
      prevConversationIdRef.current = conversationId;
      return;
    }

    // 真正的会话切换才清空内容
    console.log('🔄 MessageSender: 真正的会话切换，清空输入内容');
    setFiles([]);
    setContent('');
    setOpen(false);

    // 更新ref
    prevConversationIdRef.current = conversationId;
  }, [conversationId, setContent]);

  return (
    <Sender
      header={senderHeader}
      value={content}
      onChange={onChange}
      placeholder="请输入发送消息（Enter 发送消息， Shift + Enter 换行）"
      prefix={
        <Badge dot={files.length > 0 && !open}>
          <Button onClick={() => setOpen(!open)} icon={<LinkOutlined />} />
        </Badge>
      }
      // style={{
      //   boxShadow: '0px -2px 12px 4px #efefef',
      // }}
      loading={isRequesting}
      className={className}
      // onKeyDown={handleKeyDown}
      onSubmit={async (content) => {
        // 当文件存在时，判断是否所有文件都已上传完成
        console.log('files:', files);

        if (files?.length && !files.every((item) => item.status === 'done')) {
          message.error('请等待所有文件上传完成');
          return;
        }
        await onSubmit(content, {
          files:
            files?.map((file) => {
              return {
                filename: file.name,
                fileurl: file.url,
              };
            }) || [],
        });
        setOpen(false);
      }}
      onCancel={onCancel}
    />
  );
};
