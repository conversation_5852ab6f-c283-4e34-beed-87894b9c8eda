import { EllipsisOutlined, ShareAltOutlined } from '@ant-design/icons';
import { Prompts, Welcome } from '@ant-design/x';
import { Button, GetProp, Space } from 'antd';
// import { useMemo } from 'react';
import { IGetAppParametersResponse } from '../type';

// const renderTitle = (icon: React.ReactElement, title: string) => (
//   <Space align="start">
//     {icon}
//     <span>{title}</span>
//   </Space>
// );

interface IWelcomePlaceholderProps {
  /**
   * 应用参数
   */
  appParameters?: IGetAppParametersResponse;
  /**
   * 点击提示项时触发的回调函数
   */
  onPromptItemClick: GetProp<typeof Prompts, 'onItemClick'>;
}

/**
 * 对话内容区的欢迎占位符
 */
export const WelcomePlaceholder = (props: IWelcomePlaceholderProps) => {
  const { onPromptItemClick, appParameters } = props;

  const placeholderPromptsItems = [
    {
      key: '1',
      label: appParameters?.promptsItems?.tip_title,
      description: appParameters?.promptsItems?.description,
      children: appParameters?.promptsItems?.agent_list,
    },
  ];

  return (
    <Space direction="vertical" size={22} className="w-full px-3 box-border mx-auto mt-8">
      <Welcome
        variant="borderless"
        icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
        title={appParameters?.openingStatement}
        description={appParameters?.openingDesc}
        extra={
          <Space>
            <Button icon={<ShareAltOutlined />} />
            <Button icon={<EllipsisOutlined />} />
          </Space>
        }
      />
      <Prompts
        title="我可以帮您:"
        items={placeholderPromptsItems}
        styles={{
          item: {
            flex: 1,
            backgroundImage: 'linear-gradient(123deg, #e5f4ff 0%, #efe7ff 100%)',
            borderRadius: 12,
            border: 'none',
          },
          subItem: { background: '#ffffffa6', width: '49.3%' },
          subList: { flexDirection: 'row', flexWrap: 'wrap' },
        }}
        onItemClick={onPromptItemClick}
      />
    </Space>
  );
};
