stages:
  - build
  - deploy

include:
  remote: 'http://***************/FRONT/oig-cli-shared-utils/-/raw/master/gitlab-ci-front-tpl.yml'

variables:
  FORCE_COLOR: '1'

build_source:
  stage: build
  extends:
    - .include_build_server
  only:
    - develop
    - main
    - release
  before_script:
    # 拷贝/读取配置
    - cp ./deploy/$CI_COMMIT_REF_NAME/nginx.conf ./deploy
    - source ./deploy/$CI_COMMIT_REF_NAME/source.env
    - node -v
  script:
    # 打包前端代码
    - npm install
    - npm run build
    - npx oig-cli-shared-utils@latest build:image

# 更新容器镜像
deploy:
  stage: deploy
  only:
    - develop
    - main
    # - release
  before_script:
    - source ./deploy/$CI_COMMIT_REF_NAME/source.env
  script:
    - npx oig-cli-shared-utils@latest deploy
