import { IRetrieverResource } from '@/types/event';
import React from 'react';

interface IMessageReferrenceProps {
  /**
   * 消息引用链接列表
   */
  items?: IRetrieverResource[];
  animate?: boolean;
}

/**
 * 消息引用链接列表
 */
export default function MessageReferrence(props: IMessageReferrenceProps) {
  const { items, animate = true } = props;
  const [renderedItems, setRenderedItems] = React.useState<any[]>([]);
  React.useEffect(() => {
    if (!animate) {
      setRenderedItems(items || []);
      return;
    }
    if (items && items.length > renderedItems.length) {
      setTimeout(() => {
        setRenderedItems(items.slice(0, renderedItems.length + 1));
      }, 100);
    }
  }, [items, animate, renderedItems.length]);

  if (!items?.length) {
    return null;
  }

  const visibleItems = renderedItems;

  return (
    <div className="pb-3">
      <div className="flex items-center text-gray-400">
        <span className="mr-3 text-sm">引用</span>
        <div className="flex-1 border-gray-400 border-dashed border-0 border-t h-0" />
      </div>
      {visibleItems?.map((item, idx) => {
        const cls = `mt-2 truncate${animate && idx === visibleItems.length - 1 ? ' fade-in' : ''}`;
        return (
          <div className={cls} key={item.id}>
            <a
              className="text-gray-600"
              target="_blank"
              rel="noreferrer"
              href={item.document_name}
              title={item.document_name}
            >
              {item.document_name}
            </a>
          </div>
        );
      })}
    </div>
  );
}
