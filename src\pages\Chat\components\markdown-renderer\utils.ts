import MarkdownItPluginEcharts from '@lexmin0412/markdown-it-echarts';
import hljs from 'highlight.js';
import MarkdownIt from 'markdown-it';
import markdownItPluginKatex from 'markdown-it-katex-gpt';

const md: any = MarkdownIt({
  html: true,
  breaks: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      // 已知语言
      return `<pre class="hljs"><code>${
        hljs.highlight(str, { language: lang }).value
      }</code></pre>`;
    } else {
      // 自动检测语言
      const result = hljs.highlightAuto(str);
      return `<pre class="hljs"><code class="language-${result.language}">${result.value}</code></pre>`;
    }
  },
})
  .use(MarkdownItPluginEcharts)
  .use(markdownItPluginKatex, {
    delimiters: [
      { left: '\\[', right: '\\]', display: true },
      { left: '\\(', right: '\\)', display: false },
      { left: '$$', right: '$$', display: false },
    ],
  });

export default md;
