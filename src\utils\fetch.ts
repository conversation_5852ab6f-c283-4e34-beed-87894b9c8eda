import { message } from 'antd';
const { UMI_APP_GATWAY } = process.env;

/**
 * 未授权错误类
 */
export class UnauthorizedError extends Error {
  constructor(message: string) {
    super('Unauthorized');
    this.name = 'UnauthorizedError';
    this.message = message;
  }
}

export class XRequest {
  constructor(options: { baseURL: string; apiKey?: string }) {
    this.options = options;
  }

  options: {
    baseURL: string;
    apiKey?: string;
  };

  async baseRequest(url: string, options: RequestInit) {
    const result = await fetch(`${this.options.baseURL}${url}`, {
      ...options,
      // headers: {
      //   ...options.headers,
      //   // Authorization: `Bearer ${this.options.apiKey}`,
      // },
    });
    if (result.status === 401) {
      message.error('未授权, 请检查你的配置');
      throw new UnauthorizedError('Unauthorized');
    }
    return result;
  }

  async get(url: string, params?: { [key: string]: any }, options: RequestInit = {}) {
    const queryString = params ? `?${new URLSearchParams(params).toString()}` : '';
    const result = await this.baseRequest(`${url}${queryString}`, {
      method: 'GET',
      ...options,
    });
    return result;
  }

  async post(url: string, params?: { [key: string]: any }, options: RequestInit = {}) {
    const result = await this.baseRequest(url, {
      method: 'POST',
      body: JSON.stringify(params),
      ...options,
    });
    return result;
  }
}

export default new XRequest({
  baseURL: UMI_APP_GATWAY || '',
});
