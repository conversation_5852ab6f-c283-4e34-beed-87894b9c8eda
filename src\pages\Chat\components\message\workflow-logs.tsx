import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { Collapse } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { IAgentMessage, IWorkflowNode } from '@/types';
import { MarkdownRenderer } from '../markdown-renderer';
import <PERSON><PERSON>hain from '../thought-chain';
import styles from './content.module.css';
import WorkflowNodeIcon from './workflow-node-icon';

interface IWorkflowLogsProps {
  status: NonNullable<IAgentMessage['workflows']>['status'];
  items: IWorkflowNode[];
  animate?: boolean;
  /**
   * 是否为历史消息
   */
  isHistory?: boolean;
}

export default function WorkflowLogs(props: IWorkflowLogsProps) {
  const { items, status, animate = true, isHistory } = props;
  const prevKeyListRef = useRef<string[]>([]);

  // 管理外层 Collapse 的展开状态
  const [outerActiveKey, setOuterActiveKey] = useState<string[]>(['workflow']);

  // 管理内层 Collapse 的展开状态
  const [innerActiveKey, setInnerActiveKey] = useState<string[]>([]);

  // 当 items 变化时，更新内层 Collapse 的展开状态并滚动
  useEffect(() => {
    if (!animate || isHistory) return;

    const keyList = items.map((item) => item.id);
    setInnerActiveKey(keyList);

    // 检查 keyList 是否真正变化
    const hasNewKeys = keyList.some((key) => !prevKeyListRef.current.includes(key));
    const keysChanged = hasNewKeys;
    prevKeyListRef.current = keyList;
    // 只在 keyList 变化且有内容时执行滚动
    if (keysChanged && keyList?.length) {
      setTimeout(() => {
        const scrollContainer = document.querySelector('#scroll-content');
        if (scrollContainer instanceof HTMLElement) {
          scrollContainer.scrollTo({
            top: scrollContainer.scrollHeight,
            behavior: 'smooth',
          });
        }
      }, 280);
    }
  }, [items, animate, isHistory]);

  if (!items?.length) {
    return null;
  }

  const collapseItems = [
    {
      key: 'workflow',
      label: (
        <div className="flex items-center justify-between">
          <div>计划</div>
          {status === 'running' ? (
            <LoadingOutlined />
          ) : status === 'finished' ? (
            <div className="text-green-700 flex items-center">
              <span className="mr-2">成功</span>
              <CheckCircleOutlined className="text-green-700" />
            </div>
          ) : null}
        </div>
      ),
      children: (
        <Collapse
          size="small"
          activeKey={innerActiveKey}
          onChange={(keys) => setInnerActiveKey(keys as string[])}
          items={items.map((item, index) => {
            const totalTokens = item.execution_metadata?.total_tokens;
            return {
              key: item.id,
              label: (
                <div className={animate && index === items.length - 1 ? styles['fade-in'] : ''}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <div className="mr-2">
                        <WorkflowNodeIcon type={item.type} />
                      </div>
                      <div>{item.title}</div>
                    </div>
                    <div className="flex items-center">
                      {item.status === 'success' ? (
                        <>
                          <div>{item.elapsed_time} 秒</div>
                          <div className="ml-3">{totalTokens ? `${totalTokens} tokens` : ''}</div>
                        </>
                      ) : null}
                      {item.status === 'success' ? (
                        <CheckCircleOutlined className="text-green-700" />
                      ) : item.status === 'error' ? (
                        <CloseCircleOutlined className="text-red-700" />
                      ) : item.status === 'running' ? (
                        <LoadingOutlined />
                      ) : (
                        <InfoOutlined />
                      )}
                    </div>
                  </div>
                </div>
              ),
              children: (
                <>
                  {item.action_process?.action_type === 'tools' ? (
                    <>
                      <ThoughtChain
                        key={`thought-${index}`}
                        uniqueKey={`${item.id}-thought-${index}`}
                        items={item.action_process?.action_process}
                        className="mt-3"
                        animate={animate}
                      />
                      <MarkdownRenderer markdownText={item.outputs || ''} />
                    </>
                  ) : (
                    <>
                      <MarkdownRenderer
                        markdownText={`${
                          item?.action_process?.action_process
                            ? item?.action_process?.action_process
                            : ''
                        }`}
                      />
                      <MarkdownRenderer markdownText={item?.outputs || ''} />
                    </>
                  )}
                </>
              ),
            };
          })}
        >
          {}
        </Collapse>
      ),
    },
  ];

  return (
    <div className="min-w-chat-card my-3">
      <Collapse
        items={collapseItems}
        size="small"
        className="bg-white"
        activeKey={outerActiveKey}
        onChange={(keys) => setOuterActiveKey(keys as string[])}
      />
    </div>
  );
}
