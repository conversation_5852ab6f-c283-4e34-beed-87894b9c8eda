/**
 * 对话接口流式输出事件类型列表
 */
export enum EventEnum {
  /**
   * 消息事件，代表普通消息的发送或接收
   */
  MESSAGE = 'message',
  /**
   * 代理消息事件，代表代理发送的消息
   */
  AGENT_MESSAGE = 'agent_message',
  /**
   * 代理思考事件，代表代理在处理过程中的思考信息
   */
  AGENT_THOUGHT = 'agent_thought',
  /**
   * 消息文件事件，代表与消息相关的文件信息
   */
  MESSAGE_FILE = 'message_file',
  /**
   * 消息结束事件，代表一条消息的处理结束
   */
  MESSAGE_END = 'message_end',
  /**
   * TTS 消息事件，代表文本转语音的消息
   */
  TTS_MESSAGE = 'tts_message',
  /**
   * TTS 消息结束事件，代表文本转语音消息的处理结束
   */
  TTS_MESSAGE_END = 'tts_message_end',
  /**
   * 消息替换事件，代表对已有消息的替换操作
   */
  MESSAGE_REPLACE = 'message_replace',
  /**
   * 错误事件，代表系统出现错误的情况
   */
  ERROR = 'error',
  /**
   * 心跳事件，用于保持连接或检测服务状态
   */
  PING = 'ping',
  /**
   * 工作流开始事件，代表工作流开始执行
   */
  WORKFLOW_STARTED = 'workflow_started',
  /**
   * 工作流结束事件，代表工作流执行完成
   */
  WORKFLOW_FINISHED = 'workflow_finished',
  /**
   * 工作流节点开始事件，代表工作流中的某个节点开始执行
   */
  WORKFLOW_NODE_STARTED = 'node_started',

  /**
   * 工作流节点进行，代表工作流中的某个节点执行中
   */
  WORKFLOW_NODE_WORKING = 'node_working',
  /**
   * 工作流节点结束事件，代表工作流中的某个节点执行完成
   */
  WORKFLOW_NODE_FINISHED = 'node_finished',
}

export enum TransformEventEnum {
  /**
   * 代理消息事件，代表代理发送的消息
   */
  intention_started = EventEnum.AGENT_MESSAGE,
  /**
   * 代理思考事件，代表代理在处理过程中的思考信息
   */
  INTENTION_ENDED = EventEnum.AGENT_THOUGHT,
  /**
   * 工作流开始事件，代表工作流开始执行
   */
  PLAN_STARTED = EventEnum.WORKFLOW_STARTED,
  /**
   * 工作流结束事件，代表工作流执行完成
   */
  PLAN_ENDED = EventEnum.WORKFLOW_FINISHED,
  /**
   * 工作流节点开始事件，代表工作流中的某个节点开始执行
   */
  ACTION_STARTED = EventEnum.WORKFLOW_NODE_STARTED,
  /**
   * 工作流节点开始事件，代表工作流中的某个节点执行中
   */
  ACTION_WORKING = EventEnum.WORKFLOW_NODE_STARTED,
  /**
   * 工作流节点结束事件，代表工作流中的某个节点执行完成
   */
  ACTION_ENDED = EventEnum.WORKFLOW_NODE_FINISHED,
}

// 点赞状态
export enum RatingType {
  Good = 'Good',
  Bad = 'Bad',
  Normal = 'Normal',
}

// 角色 'local' | 'user' | 'ai'
export enum RoleType {
  local = 'local',
  user = 'user',
  ai = 'ai',
}
