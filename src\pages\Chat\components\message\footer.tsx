import { CopyOutlined, DislikeOutlined, LikeOutlined, SyncOutlined } from '@ant-design/icons';
import { copyToClipboard } from '@toolkit-fe/clipboard';
import { useRequest, useSetState } from 'ahooks';
import { message as antdMessage, Form, Modal, Space } from 'antd';

import { RatingType, RoleType } from '@/enums';
import TextArea from 'antd/es/input/TextArea';
import { useState } from 'react';
import ActionButton from './action-btn';

interface IMessageFooterProps {
  /**
   * 反馈 API
   */
  feedbackApi: (params: {
    /**
     * 反馈的消息 ID
     */
    messageId: string;
    /**
     * 反馈操作类型
     */
    // rating: IRating;
    rating: RatingType;
    /**
     * 反馈文本内容
     */
    content: string;
  }) => Promise<unknown>;
  /**
   * 消息 ID
   */
  messageId: string;
  /**
   * 消息中的文字内容部分
   */
  messageContent: string;
  /**
   * 消息中的角色
   */
  role: RoleType;
  /**
   * 用户对消息的反馈
   */
  feedback: {
    /**
     * 用户对消息的点赞/点踩/撤销操作
     */
    rating?: RatingType;
    /**
     * 操作回调
     */
    callback: () => void;
  };
}

/**
 * 消息底部操作区
 */
export default function MessageFooter(props: IMessageFooterProps) {
  const {
    messageId,
    messageContent,
    role,
    feedback: { rating, callback },
    feedbackApi,
  } = props;

  const [isUser] = useState<boolean>(role === RoleType.user);
  const [isLiked, setIsLiked] = useState<boolean>(rating === RatingType.Good);
  const [isDisLiked, setIsDisLiked] = useState<boolean>(rating === RatingType.Bad);

  const [loading, setLoading] = useSetState({
    like: false,
    dislike: false,
  });

  const [feedbackForm] = Form.useForm();

  /**
   * 用户对消息的反馈
   */
  const { runAsync } = useRequest(
    (rating: RatingType, content = '') => {
      return feedbackApi({
        messageId,
        rating,
        content,
      });
    },
    {
      manual: true,
      onSuccess() {
        antdMessage.success('操作成功');
        callback?.();
      },
      onFinally() {
        setLoading({
          like: false,
          dislike: false,
        });
      },
    },
  );

  /**
   * 用户点赞
   */
  const likeFun = async () => {
    try {
      setLoading({
        like: true,
      });
      await runAsync(isLiked ? RatingType.Normal : RatingType.Good);
      setIsLiked(!isLiked);
      setIsDisLiked(false);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading({
        like: false,
      });
    }
  };

  /**
   * 用户点踩
   */
  const dislikeFun = async () => {
    if (isDisLiked) {
      await runAsync(isDisLiked ? RatingType.Normal : RatingType.Bad);
      setIsDisLiked(!isDisLiked);
    } else {
      feedbackForm.setFieldsValue({
        content: '',
      });
      const modal = Modal.confirm({
        destroyOnClose: true,
        title: '反馈',
        okButtonProps: { loading: false, disabled: true }, // 初始状态
        content: (
          <Form form={feedbackForm} className="mt-3">
            <Form.Item name="content">
              <TextArea
                style={{ height: 120 }}
                placeholder="请输入"
                onChange={(e) => {
                  // 更新按钮状态
                  const isEmpty = !e.target.value.trim();
                  modal.update({
                    okButtonProps: {
                      disabled: isEmpty,
                      loading: false,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form>
        ),
        onOk: async () => {
          try {
            modal.update({
              okButtonProps: { loading: true, disabled: true },
            });
            const values = await feedbackForm.validateFields();
            await runAsync(isDisLiked ? RatingType.Normal : RatingType.Bad, values.content);
            setIsDisLiked(!isDisLiked);
            setIsLiked(false);
            modal.destroy();
          } catch (err) {
            console.error(err);
          } finally {
            modal.update({
              okButtonProps: { loading: false },
            });
          }
        },
      });
    }
  };

  /**
   * 操作按钮列表
   */
  const actionButtons = [
    {
      icon: <SyncOutlined />,
      hidden: true,
    },
    {
      icon: <CopyOutlined />,
      onClick: async () => {
        await copyToClipboard(messageContent);
        antdMessage.success('复制成功');
      },
      active: false,
      loading: false,
      hidden: false,
    },
    {
      icon: <LikeOutlined />,
      onClick: likeFun,
      active: isLiked,
      loading: loading.like,
      hidden: isUser,
    },
    {
      icon: <DislikeOutlined />,
      onClick: dislikeFun,
      active: isDisLiked,
      loading: loading.dislike,
      hidden: isUser,
    },
  ];

  return (
    <Space>
      {actionButtons.map(
        (buttonProps, index) =>
          !buttonProps.hidden && (
            <ActionButton
              key={index}
              icon={buttonProps.icon}
              onClick={buttonProps.onClick}
              active={buttonProps.active}
              loading={buttonProps.loading}
            />
          ),
      )}
    </Space>
  );
}
