@tailwind components;
@tailwind utilities;

.ant-bubble {
	.ant-bubble-content {
		padding-top: 0;
		padding-bottom: 0;
		min-height: auto;

		think,
		details {
			background-color: transparent !important;
			padding: 0 !important;
			margin-top: 0.5rem;
			color: gray;
		}
	}

	&[role='user'] {
		.ant-bubble-content {
			background-color: #efeffd;
			color: #fff;
		}
	}

	p {
		margin-top: 0.5rem !important;
		margin-bottom: 0.5rem !important;
	}
}

hr {
	margin: 0.25rem auto;
	color: #9ca3b3;
	height: 0;
	display: none;
}

h3 {
	margin-top: 0.5rem !important;
	margin-bottom: 0.5rem !important;
	font-size: 18px !important;
}

body {
	margin: 0;
	color: #333;
	font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
	background: white;
}

.content {
	display: flex;
	min-height: 100vh;
	line-height: 1.1;
	text-align: center;
	flex-direction: column;
	justify-content: center;
	h1 {
		font-size: 3.6rem;
		font-weight: 700;
	}

	p {
		font-size: 1.2rem;
		font-weight: 400;
		opacity: 0.5;
	}
}

/* 滚动条的宽度 */

::-webkit-scrollbar {
	width: 6px; /*  */
}

::-webkit-scrollbar:horizontal {
  height: 6px;
}

/* 滚动条的轨道颜色 */

::-webkit-scrollbar-track {
	border-radius: 4px;
	background-color: #f0f0f0;
}

/* 滚动条的滑块样式 */

::-webkit-scrollbar-thumb {
	background-color: #8B8B8B;
	border-radius: 4px;
}

/* 滚动条在鼠标悬停时的滑块颜色 */

::-webkit-scrollbar-thumb:hover {
	background-color: #636363;
}
