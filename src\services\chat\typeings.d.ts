// @ts-ignore
/* eslint-disable */

import { RatingType } from '@/enums';
import { IActionProcess, IMessageFileItem } from '@/types';

declare namespace ChatAPI {
  // 发送信息
  type SendMessageReq = {
    prompt?: string;
    user_id?: number;
    session_id?: string;
    files?: IMessageFileItem[];
  };

  type CancelAgentReq = {
    message_id: string;
  };

  // 获取当前的用户
  type GetUserInfoReq = {
    user_id?: number;
  };
  type GetUserInfoRes = {
    user_id: number;
    username: string;
    email: string;
    password: string;
    created_time: string;
    updated_time: string;
    sessions: Array<{
      session_id: number;
      user_id: number;
      session_name: string;
      created_time: string;
      updated_time: string;
    }>;
  };

  // 获取会话列表
  type GetSessionInfoReq = {
    session_id?: number | string;
  };
  type GetSessionInfoRes = {
    session_id?: number;
    user_id?: number;
    session_name?: string;
    created_time?: string;
    updated_time?: string;
    messages?: Array<Message>;
  };
  type Message = {
    event_type: string;
    message_id: number;
    user_id: number;
    session_id: number;
    content?: string;
    created_time: string;
    updated_time: string;
    group?: string;
    intention_id?: number;
    intention_comprehend: any;
    intention_think: any;
    intention_result: any;
    intention_status?: string;
    plans?: Array<Plans>;
    is_good?: RatingType;
  };
  type Plans = {
    plan_id: number;
    user_id: number;
    session_id: number;
    message_id: number;
    intention_id: number;
    plan_name: string;
    plan_description: any;
    plan_think: any;
    plan_result: any;
    plan_status: string;
    created_time: string;
    updated_time: string;
    actions: Array<Action>;
  };
  type Action = {
    action_id: number;
    user_id: number;
    session_id: number;
    message_id: number;
    intention_id: number;
    plan_id: number;
    action_name: string;
    action_description: any;
    action_process: IActionProcess;
    action_result: any;
    action_status: string;
    created_time: string;
    updated_time: string;
    action_duration: number;
  };

  // 创建会话
  type CreateSessionReq = {
    session_id: number;
    user_id: number;
    session_name: any;
    created_time: string;
    updated_time: string;
    messages: Array<any>;
  };

  // 重命名会话
  type RenameSessionReq = {
    session_id: string;
    name: string;
  };

  // 删除会话
  type DeleteSessionReq = {
    session_id: string;
  };

  // 文件上传
  type UploadFileReq = {
    filename?: string;
    fileurl?: string;
  };

  // 获取提示集
  type GetTipsRes = {
    agent_list: Array<AgentList>;
    description: string;
    tip_title: string;
  };
  type AgentList = {
    key: string;
    description: string;
    prompt_template: string;
  };

  // 用户评论
  type CommentReq = {
    intention_id?: number;
    is_good?: RatingType;
    user_comment?: string;
  };
  type CommentRes = {};
}
